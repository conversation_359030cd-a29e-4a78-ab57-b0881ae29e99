{"mcpServers": {"pricing": {"command": "python", "args": ["-m", "awslabs.aws_pricing_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/aws-pricing-mcp-server"}}, "cost-explorer": {"command": "python", "args": ["-m", "awslabs.cost_explorer_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/cost-explorer-mcp-server"}}, "cfn": {"command": "python", "args": ["-m", "awslabs.cfn_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/cfn-mcp-server"}}, "cdk": {"command": "python", "args": ["-m", "awslabs.cdk_mcp_server.core.server"], "env": {"PYTHONPATH": "mcp/src/cdk-mcp-server"}}, "terraform": {"command": "python", "args": ["-m", "awslabs.terraform_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/terraform-mcp-server"}}, "aws-api": {"command": "python", "args": ["-m", "awslabs.aws_api_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/aws-api-mcp-server"}}, "iam": {"command": "python", "args": ["-m", "awslabs.iam_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/iam-mcp-server"}}, "well-architected": {"command": "python", "args": ["-m", "awslabs.well_architected_security_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/well-architected-security-mcp-server"}}, "cloudwatch": {"command": "python", "args": ["-m", "awslabs.cloudwatch_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/cloudwatch-mcp-server"}}, "cloudwatch-appsignals": {"command": "python", "args": ["-m", "awslabs.cloudwatch_appsignals_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/cloudwatch-appsignals-mcp-server"}}, "lambda-tool": {"command": "python", "args": ["-m", "awslabs.lambda_tool_mcp_server.server"], "env": {"PYTHONPATH": "mcp/src/lambda-tool-mcp-server"}}}}