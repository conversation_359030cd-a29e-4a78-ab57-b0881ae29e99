import shutil
import os
import json
import asyncio
import uuid
import time
from typing import List, Dict, Any, Optional, Set
from dataclasses import dataclass, field
from dotenv import load_dotenv
from contextlib import AsyncExitStack
from mcp.client.stdio import stdio_client
import boto3
from mcp import ClientSession, StdioServerParameters
import logging
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from collections import deque
from enum import Enum

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FastAPI app
app = FastAPI(title="AWS Pricing Optimization Agent API", version="1.0.0")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure this properly for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables for managing the chat session
chat_session = None
initialization_lock = asyncio.Lock()
is_initialized = False

# ------------------------
# Circuit Breaker Implementation
# ------------------------
class CircuitState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

class CircuitBreaker:
    def __init__(self, failure_threshold: int = 5, recovery_timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = CircuitState.CLOSED

    async def call(self, func, *args, **kwargs):
        if self.state == CircuitState.OPEN:
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = CircuitState.HALF_OPEN
            else:
                raise Exception("Circuit breaker is OPEN")

        try:
            result = await func(*args, **kwargs)
            if self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.failure_count >= self.failure_threshold:
                self.state = CircuitState.OPEN
                
            raise e

# ------------------------
# Pydantic Models
# ------------------------
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    stream: bool = False

class ChatResponse(BaseModel):
    response: str
    session_id: str
    status: str

class HealthResponse(BaseModel):
    status: str
    initialized: bool
    available_tools: int
    active_servers: int

# ------------------------
# Configuration Loader
# ------------------------
class Configuration:
    def __init__(self):
        load_dotenv()
        self.model_id = os.getenv("BEDROCK_MODEL_ID")
        self.region = os.getenv("AWS_REGION", "us-east-1")
        self.max_conversation_length = int(os.getenv("MAX_CONVERSATION_LENGTH", "20"))  # Reduced from 50
        self.max_concurrent_tools = int(os.getenv("MAX_CONCURRENT_TOOLS", "5"))
        self.use_compressed_schemas = os.getenv("USE_COMPRESSED_SCHEMAS", "true").lower() == "true"
        self.enable_thinking_logs = os.getenv("ENABLE_THINKING_LOGS", "false").lower() == "true"

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        with open(file_path, "r") as f:
            return json.load(f)

# ------------------------
# Tool Class (Enhanced)
# ------------------------
class Tool:
    """Represents a tool with its properties and formatting."""

    def __init__(
        self,
        name: str,
        description: str,
        input_schema: dict[str, Any],
        server_name: str,
        title: Optional[str] = None,
    ) -> None:
        self.name: str = name
        self.title: Optional[str] = title
        self.description: str = description
        self.input_schema: dict[str, Any] = input_schema
        self.server_name: str = server_name

    def format_for_llm(self) -> str:
        """Format tool information for LLM."""
        args_desc = []
        if "properties" in self.input_schema:
            for param_name, param_info in self.input_schema["properties"].items():
                arg_desc = (
                    f"- {param_name}: {param_info.get('description', 'No description')}"
                )
                if param_name in self.input_schema.get("required", []):
                    arg_desc += " (required)"
                args_desc.append(arg_desc)

        output = f"Tool: {self.name} (from {self.server_name})\n"
        
        if self.title:
            output += f"User-readable title: {self.title}\n"

        output += f"""Description: {self.description}
Arguments:
{chr(10).join(args_desc) if args_desc else 'No arguments required'}
"""
        return output

# ------------------------
# Enhanced Server Pool Class
# ------------------------
@dataclass
class ServerMetrics:
    success_count: int = 0
    failure_count: int = 0
    avg_response_time: float = 0.0
    last_used: float = field(default_factory=time.time)

class ServerPool:
    """Manages a pool of MCP servers with connection reuse and health monitoring."""
    
    def __init__(self):
        self._servers: Dict[str, 'Server'] = {}
        self._metrics: Dict[str, ServerMetrics] = {}
        self._lock = asyncio.Lock()
        self._health_check_interval = 300  # 5 minutes
        self._health_check_task: Optional[asyncio.Task] = None

    async def add_server(self, name: str, config: dict[str, Any]) -> 'Server':
        """Add a server to the pool."""
        async with self._lock:
            if name in self._servers:
                return self._servers[name]
            
            server = Server(name, config)
            await server.initialize()
            
            self._servers[name] = server
            self._metrics[name] = ServerMetrics()
            
            logger.info(f"Added server {name} to pool")
            return server

    async def get_server(self, name: str) -> 'Server':
        """Get a server from the pool."""
        async with self._lock:
            if name not in self._servers:
                raise ValueError(f"Server {name} not found in pool")
            
            server = self._servers[name]
            if not server._initialized:
                await server.initialize()
            
            self._metrics[name].last_used = time.time()
            return server

    async def get_all_servers(self) -> List['Server']:
        """Get all active servers."""
        async with self._lock:
            return list(self._servers.values())

    def record_success(self, server_name: str, response_time: float):
        """Record successful operation."""
        if server_name in self._metrics:
            metrics = self._metrics[server_name]
            metrics.success_count += 1
            # Exponential moving average
            metrics.avg_response_time = (
                0.7 * metrics.avg_response_time + 0.3 * response_time
            )

    def record_failure(self, server_name: str):
        """Record failed operation."""
        if server_name in self._metrics:
            self._metrics[server_name].failure_count += 1

    async def start_health_checks(self):
        """Start periodic health checks."""
        self._health_check_task = asyncio.create_task(self._periodic_health_check())

    async def _periodic_health_check(self):
        """Periodic health check for all servers."""
        while True:
            try:
                await asyncio.sleep(self._health_check_interval)
                
                async with self._lock:
                    for name, server in self._servers.items():
                        try:
                            if server._initialized and server.session:
                                # Simple ping by listing tools
                                await asyncio.wait_for(server.list_tools(), timeout=10.0)
                                logger.debug(f"Health check passed for {name}")
                        except Exception as e:
                            logger.warning(f"Health check failed for {name}: {e}")
                            await server.cleanup()
                            
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health check: {e}")

    async def cleanup_all(self):
        """Clean up all servers."""
        if self._health_check_task:
            self._health_check_task.cancel()
            
        async with self._lock:
            cleanup_tasks = []
            for server in self._servers.values():
                cleanup_tasks.append(server.cleanup())
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self._servers.clear()
            self._metrics.clear()

# ------------------------
# Enhanced Server Class
# ------------------------
@dataclass
class Server:
    """Manages MCP server connections and tool execution with enhanced error handling."""

    def __init__(self, name: str, config: dict[str, Any]) -> None:
        self.name: str = name
        self.config: dict[str, Any] = config
        self.session: Optional[ClientSession] = None
        self._cleanup_lock: asyncio.Lock = asyncio.Lock()
        self.exit_stack: AsyncExitStack = AsyncExitStack()
        self._tools: List[Tool] = []
        self._initialized = False
        self.circuit_breaker = CircuitBreaker()
        self._tool_cache: Dict[str, List[Tool]] = {}
        self._cache_ttl = 300  # 5 minutes
        self._last_tool_fetch = 0

    async def initialize(self) -> None:
        """Initialize the server connection with retry logic."""
        if self._initialized:
            return
            
        max_retries = 3
        base_delay = 1.0
        
        for attempt in range(max_retries):
            try:
                command = self.config.get("command")
                if not command:
                    raise ValueError(f"No command specified for server {self.name}")
                
                logger.info(f"Initializing server {self.name}, attempt {attempt + 1}")
                
                # Handle npx command
                if command == "npx":
                    command = shutil.which("npx")
                    if command is None:
                        raise ValueError("npx command not found in PATH")

                server_params = StdioServerParameters(
                    command=command,
                    args=self.config.get("args", []),
                    env={**os.environ, **self.config.get("env", {})}
                )
                
                # Add timeout to prevent hanging
                stdio_transport = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(stdio_client(server_params)),
                    timeout=30.0
                )
                
                read, write = stdio_transport
                session = await asyncio.wait_for(
                    self.exit_stack.enter_async_context(ClientSession(read, write)),
                    timeout=30.0
                )
                
                await asyncio.wait_for(session.initialize(), timeout=30.0)
                
                self.session = session
                self._initialized = True
                logger.info(f"Successfully initialized server: {self.name}")
                return
                
            except Exception as e:
                logger.error(f"Error initializing server {self.name} (attempt {attempt + 1}): {e}")
                
                if attempt < max_retries - 1:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"Retrying in {delay} seconds...")
                    await asyncio.sleep(delay)
                else:
                    await self.cleanup()
                    raise

    async def list_tools(self) -> List[Tool]:
        """List available tools from the server with caching."""
        current_time = time.time()
        
        # Check cache first
        if (self.name in self._tool_cache and 
            current_time - self._last_tool_fetch < self._cache_ttl):
            return self._tool_cache[self.name]
        
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            tools_response = await asyncio.wait_for(
                self.session.list_tools(), 
                timeout=15.0
            )
            self._tools = []

            # Handle the response structure correctly
            if hasattr(tools_response, 'tools'):
                tools_list = tools_response.tools
            elif isinstance(tools_response, list):
                tools_list = tools_response
            else:
                tools_list = []
                for item in tools_response:
                    if isinstance(item, tuple) and len(item) == 2 and item[0] == "tools":
                        tools_list.extend(item[1])

            for tool in tools_list:
                self._tools.append(Tool(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.inputSchema,
                    server_name=self.name
                ))

            # Update cache
            self._tool_cache[self.name] = self._tools
            self._last_tool_fetch = current_time
            
            return self._tools
            
        except Exception as e:
            logger.error(f"Error listing tools for server {self.name}: {e}")
            return []

    async def execute_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        timeout: float = 30.0,
    ) -> Any:
        """Execute a tool with circuit breaker and timeout."""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        async def _execute():
            logger.info(f"Executing {tool_name} on server {self.name}...")
            result = await asyncio.wait_for(
                self.session.call_tool(tool_name, arguments),
                timeout=timeout
            )
            return result

        return await self.circuit_breaker.call(_execute)

    async def cleanup(self) -> None:
        """Clean up server resources with proper error handling."""
        async with self._cleanup_lock:
            if not self._initialized:
                return
                
            try:
                logger.info(f"Cleaning up server: {self.name}")
                self._initialized = False
                self.session = None
                
                try:
                    await asyncio.wait_for(self.exit_stack.aclose(), timeout=5.0)
                except asyncio.TimeoutError:
                    logger.warning(f"Cleanup timeout for {self.name}")
                except Exception as e:
                    logger.warning(f"Cleanup error for {self.name}: {e}")
                
                logger.info(f"Successfully cleaned up server: {self.name}")
                
            except Exception as e:
                logger.warning(f"Cleanup warning for {self.name}: {e}")

# ------------------------
# Enhanced Tool Handler Class
# ------------------------
class ToolHandler:
    def __init__(self, server_pool: ServerPool, max_concurrent_tools: int = 5):
        self.server_pool = server_pool
        self.tool_to_server: Dict[str, str] = {}
        self.max_concurrent_tools = max_concurrent_tools
        self._semaphore = asyncio.Semaphore(max_concurrent_tools)

    async def initialize_all_servers(self, server_configs: Dict[str, dict]):
        """Initialize all servers using the server pool."""
        initialization_tasks = []
        
        for name, config in server_configs.items():
            task = self.server_pool.add_server(name, config)
            initialization_tasks.append(task)
        
        # Initialize servers concurrently
        servers = await asyncio.gather(*initialization_tasks, return_exceptions=True)
        
        # Build tool mapping
        await self._build_tool_mapping()

    async def _build_tool_mapping(self):
        """Build mapping of tool names to servers."""
        servers = await self.server_pool.get_all_servers()
        
        for server in servers:
            try:
                tools = await server.list_tools()
                for tool in tools:
                    if tool.name in self.tool_to_server:
                        logger.warning(f"Tool {tool.name} exists in multiple servers. Using server {self.tool_to_server[tool.name]}")
                    else:
                        self.tool_to_server[tool.name] = server.name
            except Exception as e:
                logger.error(f"Failed to list tools for server {server.name}: {e}")

    async def list_all_tools(self) -> List[Tool]:
        """List all tools from all servers with caching."""
        all_tools = []
        servers = await self.server_pool.get_all_servers()
        
        # Use asyncio.gather for concurrent tool listing
        tool_listing_tasks = []
        for server in servers:
            task = server.list_tools()
            tool_listing_tasks.append(task)
        
        results = await asyncio.gather(*tool_listing_tasks, return_exceptions=True)
        
        for server, result in zip(servers, results):
            if isinstance(result, Exception):
                logger.error(f"Error listing tools for {server.name}: {result}")
            else:
                all_tools.extend(result)

        return all_tools

    async def execute_tool(self, tool_name: str, arguments: dict[str, Any]) -> Any:
        """Execute a tool on the appropriate server with concurrency control."""
        async with self._semaphore:
            server_name = self.tool_to_server.get(tool_name)
            if not server_name:
                raise ValueError(f"Tool {tool_name} not found in any server")
            
            start_time = time.time()
            try:
                server = await self.server_pool.get_server(server_name)
                result = await server.execute_tool(tool_name, arguments)
                
                # Record success metrics
                response_time = time.time() - start_time
                self.server_pool.record_success(server_name, response_time)
                
                return result
                
            except Exception as e:
                self.server_pool.record_failure(server_name)
                raise

    async def execute_tools_concurrently(self, tool_calls: List[Dict[str, Any]]) -> List[Any]:
        """Execute multiple tools concurrently."""
        if len(tool_calls) > self.max_concurrent_tools:
            # Split into batches
            results = []
            for i in range(0, len(tool_calls), self.max_concurrent_tools):
                batch = tool_calls[i:i + self.max_concurrent_tools]
                batch_results = await self._execute_tool_batch(batch)
                results.extend(batch_results)
            return results
        else:
            return await self._execute_tool_batch(tool_calls)

    async def _execute_tool_batch(self, tool_calls: List[Dict[str, Any]]) -> List[Any]:
        """Execute a batch of tool calls concurrently."""
        tasks = []
        for tool_call in tool_calls:
            task = self.execute_tool(
                tool_call['name'],
                tool_call['arguments']
            )
            tasks.append(task)
        
        return await asyncio.gather(*tasks, return_exceptions=True)

    async def cleanup_all(self):
        """Clean up all servers."""
        await self.server_pool.cleanup_all()

# ------------------------
# Bedrock Client (Enhanced)
# ------------------------
class BedrockClient:
    def __init__(self, model_id: str, region: str):
        self.model_id = model_id
        self.session = boto3.Session()
        self.bedrock_runtime = self.session.client("bedrock-runtime", region_name="ap-south-1")
        self.token_usage_stats = {"input_tokens": 0, "output_tokens": 0, "requests": 0}
        self.circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=30.0)

    async def converse(self, messages: List[Dict[str, Any]], tools: List[Dict[str, Any]]) -> dict:
        """Converse with Bedrock using circuit breaker and token tracking."""
        async def _converse():
            # Track request
            self.token_usage_stats["requests"] += 1

            response = self.bedrock_runtime.converse(
                modelId=self.model_id,
                messages=messages,
                inferenceConfig={"maxTokens": 1500, "temperature": 0.3},  # Reduced max tokens
                toolConfig={"tools": tools} if tools else {},
                system=[
                    {
                        "text": "AWS Agent with 3 MCP servers: pricing (get costs), cost-explorer (billing data), cfn (resources). Be concise, use tools efficiently, avoid loops."
                    }
                ]
            )

            # Track token usage
            usage = response.get('usage', {})
            self.token_usage_stats["input_tokens"] += usage.get('inputTokens', 0)
            self.token_usage_stats["output_tokens"] += usage.get('outputTokens', 0)

            return response

        try:
            return await self.circuit_breaker.call(_converse)
        except Exception as e:
            logger.error(f"Error in Bedrock converse: {e}")
            raise

# ------------------------
# Conversation Manager (New)
# ------------------------
class ConversationManager:
    """Manages conversation state with smart truncation and summarization."""

    def __init__(self, max_length: int = 50):
        self.max_length = max_length
        self.messages: deque = deque(maxlen=max_length * 2)  # Buffer for truncation
        self.important_messages: Set[int] = set()  # Track important message indices
        self.tool_execution_history: deque = deque(maxlen=10)  # Track recent tool calls
        self.last_activity = time.time()

    def add_message(self, message: Dict[str, Any], important: bool = False):
        """Add a message to the conversation."""
        message_index = len(self.messages)
        self.messages.append(message)
        
        if important:
            self.important_messages.add(message_index)

    def get_messages_for_llm(self) -> List[Dict[str, Any]]:
        """Get aggressively optimized message list for LLM processing."""
        messages_list = list(self.messages)

        if len(messages_list) <= self.max_length:
            return self._compress_message_content(messages_list)

        # Aggressive truncation: keep only essential context
        recent_messages = messages_list[-self.max_length//3:]  # Keep minimal recent context
        important_msgs = []

        # Keep only the most important messages
        for idx in self.important_messages:
            if idx < len(messages_list) - self.max_length//3 and len(important_msgs) < 3:
                important_msgs.append(messages_list[idx])

        # Combine and compress
        truncated = important_msgs + recent_messages

        # Add concise truncation notice
        if len(truncated) < len(messages_list):
            truncation_notice = {
                "role": "assistant",
                "content": [{"text": f"[{len(messages_list) - len(truncated)} msgs truncated]"}]
            }
            truncated.insert(-len(recent_messages), truncation_notice)

        return self._compress_message_content(truncated[-self.max_length//2:])

    def _compress_message_content(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Compress individual message content to reduce tokens."""
        compressed = []
        for msg in messages:
            if msg.get("role") == "assistant" and "content" in msg:
                content = msg["content"]
                if isinstance(content, list) and len(content) > 0 and "text" in content[0]:
                    text = content[0]["text"]

                    # Remove verbose thinking sections
                    if "[Thinking:" in text and "]" in text:
                        parts = text.split("]")
                        if len(parts) > 1:
                            text = parts[-1].strip()

                    # Remove tool execution logs
                    if "[Executed tool" in text:
                        lines = text.split("\n")
                        text = "\n".join([line for line in lines if not line.startswith("[Executed tool")])

                    # Truncate very long responses
                    if len(text) > 500:
                        text = text[:500] + "..."

                    compressed.append({**msg, "content": [{"text": text}]})
                else:
                    compressed.append(msg)
            else:
                compressed.append(msg)

        return compressed

    def add_tool_execution(self, tool_name: str, args: dict) -> bool:
        """Add tool execution to history and check for loops."""
        execution_key = f"{tool_name}:{hash(str(sorted(args.items())))}"

        # Check for recent duplicates (loop detection)
        recent_executions = list(self.tool_execution_history)[-5:]  # Last 5 executions
        if recent_executions.count(execution_key) >= 3:
            return False  # Loop detected

        self.tool_execution_history.append(execution_key)
        return True

    def clear(self):
        """Clear conversation history."""
        self.messages.clear()
        self.important_messages.clear()
        self.tool_execution_history.clear()

# ------------------------
# Message Helper Classes (Enhanced)
# ------------------------
@dataclass
class Message:
    role: str
    content: List[Dict[str, Any]]

    @classmethod
    def user(cls, text: str) -> 'Message':
        return cls(role="user", content=[{"text": text}])

    @classmethod
    def assistant(cls, text: str) -> 'Message':
        return cls(role="assistant", content=[{"text": text}])

    @classmethod
    def tool_result(cls, tool_use_id: str, output: Any) -> Dict[str, Any]:
        """Create a properly formatted tool result message."""
        try:
            if hasattr(output, 'content'):
                content_list = []
                for c in output.content:
                    if hasattr(c, 'text'):
                        content_list.append({"text": c.text})
                    else:
                        content_list.append({"text": str(c)})
            else:
                content_list = [{"text": str(output)}]

            return {
                "role": "user",
                "content": [
                    {
                        "toolResult": {
                            "toolUseId": tool_use_id,
                            "content": content_list
                        }
                    }
                ]
            }
        except Exception as e:
            logger.error(f"Error formatting tool result: {e}")
            return {
                "role": "user",
                "content": [
                    {
                        "toolResult": {
                            "toolUseId": tool_use_id,
                            "content": [{"text": f"Error formatting result: {str(e)}"}]
                        }
                    }
                ]
            }

    @classmethod
    def tool_request(cls, tool_use_id: str, name: str, input_data: dict) -> 'Message':
        return cls(
            role="assistant",
            content=[{
                "toolUse": {
                    "toolUseId": tool_use_id,
                    "name": name,
                    "input": input_data
                }
            }]
        )

class ToolWrapper:
    def __init__(self, name: str, description: str, input_schema: dict[str, Any]):
        self.name = name
        self.description = description
        self.input_schema = input_schema
        self.compressed_schema = self._compress_schema(input_schema)

    def _compress_schema(self, schema: dict) -> dict:
        """Compress schema by removing verbose descriptions and examples."""
        if not isinstance(schema, dict):
            return schema

        compressed = {}
        for key, value in schema.items():
            if key in ['description', 'title'] and len(str(value)) > 100:
                # Truncate long descriptions to save tokens
                compressed[key] = str(value)[:100] + "..."
            elif key == 'examples':
                # Remove examples completely
                continue
            elif key == '$defs':
                # Simplify nested definitions
                compressed[key] = self._simplify_defs(value)
            elif isinstance(value, dict):
                compressed[key] = self._compress_schema(value)
            elif isinstance(value, list):
                compressed[key] = [self._compress_schema(item) if isinstance(item, dict) else item for item in value]
            else:
                compressed[key] = value

        return compressed

    def _simplify_defs(self, defs: dict) -> dict:
        """Simplify schema definitions to essential properties only."""
        simplified = {}
        for def_name, def_value in defs.items():
            if isinstance(def_value, dict):
                # Keep only essential properties
                simplified[def_name] = {
                    "type": def_value.get("type", "object"),
                    "properties": {
                        prop: {"type": prop_def.get("type", "string")}
                        for prop, prop_def in def_value.get("properties", {}).items()
                    },
                    "required": def_value.get("required", [])
                }
            else:
                simplified[def_name] = def_value
        return simplified

    def to_bedrock_format(self, use_compressed: bool = True) -> Dict:
        """Convert tool to Bedrock format with optional compression."""
        schema = self.compressed_schema if use_compressed else self.input_schema

        return {
            "toolSpec": {
                "name": self.name,
                "description": self.description[:200] + "..." if len(self.description) > 200 else self.description,
                "inputSchema": {
                    "json": {
                        "type": "object",
                        "properties": schema.get("properties", {}),
                        "required": schema.get("required", [])
                    }
                },
            }
        }

# ------------------------
# Enhanced Chat Session Class
# ------------------------
class ChatSession:
    def __init__(self, server_pool: ServerPool, bedrock: BedrockClient, config: Configuration):
        self.server_pool = server_pool
        self.bedrock = bedrock
        self.config = config
        self.tool_handler = ToolHandler(server_pool, config.max_concurrent_tools)
        self.tools = []
        self.bedrock_tools = []
        self.conversations: Dict[str, ConversationManager] = {}
        self._session_lock = asyncio.Lock()

    async def initialize(self):
        """Initialize the chat session."""
        # Load server configuration
        config_data = Configuration.load_config("servers_config.json")
        
        # Initialize servers through tool handler
        await self.tool_handler.initialize_all_servers(config_data["mcpServers"])
        
        # Start health checks
        await self.server_pool.start_health_checks()
        
        # Get all tools from all servers
        all_tools = await self.tool_handler.list_all_tools()
        self.tools = [ToolWrapper(t.name, t.description, t.input_schema) for t in all_tools]
        self.bedrock_tools = [tool.to_bedrock_format(use_compressed=self.config.use_compressed_schemas) for tool in self.tools]
        
        logger.info(f"Chat session initialized with {len(all_tools)} tools from {len(await self.server_pool.get_all_servers())} servers")

    async def get_or_create_conversation(self, session_id: str) -> ConversationManager:
        """Get or create a conversation manager for a session."""
        async with self._session_lock:
            if session_id not in self.conversations:
                self.conversations[session_id] = ConversationManager(self.config.max_conversation_length)
            return self.conversations[session_id]

    async def process_message(self, message: str, session_id: str) -> str:
        """Process a single message and return the response."""
        conversation = await self.get_or_create_conversation(session_id)
        
        # Add user message
        conversation.add_message(Message.user(message).__dict__)
        
        # Get optimized message history
        messages = conversation.get_messages_for_llm()
        
        # Process with Bedrock
        response = await self.bedrock.converse(messages, self.bedrock_tools)
        final_response = await self.process_response(response, conversation)
        
        return final_response

    async def process_message_stream(self, message: str, session_id: str):
        """Process a message with streaming response."""
        conversation = await self.get_or_create_conversation(session_id)
        conversation.add_message(Message.user(message).__dict__)
        
        # For now, we'll simulate streaming by yielding chunks
        # In a real implementation, you'd use Bedrock's streaming API
        response = await self.process_message(message, session_id)
        
        # Simulate streaming by yielding words
        words = response.split()
        for i, word in enumerate(words):
            chunk = word + (" " if i < len(words) - 1 else "")
            yield f"data: {json.dumps({'chunk': chunk, 'done': False})}\n\n"
            await asyncio.sleep(0.05)  # Simulate streaming delay
        
        yield f"data: {json.dumps({'chunk': '', 'done': True})}\n\n"

    async def _handle_tool_call(self, tool_info: Dict, conversation: ConversationManager) -> List[str]:
        """Handle a tool call with enhanced error handling and loop detection."""
        tool_name = tool_info['name']
        tool_args = tool_info['input']
        tool_use_id = tool_info['toolUseId']

        # Check for tool execution loops
        if not conversation.add_tool_execution(tool_name, tool_args):
            logger.warning(f"Tool execution loop detected for {tool_name}, skipping")
            return [f"[Tool execution loop detected for {tool_name}, skipping to prevent infinite loops]"]

        try:
            logger.info(f"Executing tool: {tool_name} with args: {tool_args}")
            result = await self.tool_handler.execute_tool(tool_name, tool_args)
            
            # Add tool request message
            tool_request_msg = Message.tool_request(tool_use_id, tool_name, tool_args).__dict__
            conversation.add_message(tool_request_msg)
            
            # Add tool result message
            tool_result_msg = Message.tool_result(tool_use_id, result)
            conversation.add_message(tool_result_msg, important=True)  # Mark tool results as important

            return [f"[Executed tool {tool_name}]"]
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            # Add error result
            error_result = {
                "role": "user",
                "content": [
                    {
                        "toolResult": {
                            "toolUseId": tool_use_id,
                            "content": [{"text": f"Error executing tool: {str(e)}"}],
                            "isError": "True"
                        }
                    }
                ]
            }
            conversation.add_message(error_result)
            return [f"[Error executing tool {tool_name}: {str(e)}]"]

    async def _handle_concurrent_tool_calls(self, tool_calls: List[Dict], conversation: ConversationManager) -> List[str]:
        """Handle multiple tool calls concurrently."""
        results = await self.tool_handler.execute_tools_concurrently(tool_calls)
        execution_results = []
        
        for tool_call, result in zip(tool_calls, results):
            tool_use_id = tool_call['toolUseId']
            tool_name = tool_call['name']
            
            # Add tool request message
            tool_request_msg = Message.tool_request(tool_use_id, tool_name, tool_call['input']).__dict__
            conversation.add_message(tool_request_msg)
            
            if isinstance(result, Exception):
                # Handle error
                error_result = {
                    "role": "user",
                    "content": [
                        {
                            "toolResult": {
                                "toolUseId": tool_use_id,
                                "content": [{"text": f"Error executing tool: {str(result)}"}],
                                "isError": "True"
                            }
                        }
                    ]
                }
                conversation.add_message(error_result)
                execution_results.append(f"[Error executing tool {tool_name}: {str(result)}]")
            else:
                # Handle success
                tool_result_msg = Message.tool_result(tool_use_id, result)
                conversation.add_message(tool_result_msg, important=True)
                execution_results.append(f"[Executed tool {tool_name}]")
        
        return execution_results
    
    async def process_response(self, response: dict, conversation: ConversationManager) -> str:
        """Process the response from Bedrock with enhanced tool handling."""
        final_text = []
        MAX_TURNS = 15  # Reduced to prevent excessive loops
        turn_count = 0

        while turn_count < MAX_TURNS:
            stop_reason = response.get('stopReason', 'unknown')
            
            if stop_reason == 'tool_use':
                # Handle tool use
                message_content = response['output']['message']['content']
                tool_calls = []
                
                for item in message_content:
                    if 'text' in item and item['text'].strip():
                        thinking_text = f"[Thinking: {item['text']}]"
                        final_text.append(thinking_text)
                        conversation.add_message(Message.assistant(item['text']).__dict__)
                    elif 'toolUse' in item:
                        tool_calls.append(item['toolUse'])
                
                # Execute tools (concurrently if multiple)
                if len(tool_calls) > 1:
                    logger.info(f"Executing {len(tool_calls)} tools concurrently")
                    results = await self._handle_concurrent_tool_calls(tool_calls, conversation)
                elif len(tool_calls) == 1:
                    results = await self._handle_tool_call(tool_calls[0], conversation)
                else:
                    results = []
                
                final_text.extend(results)
                
                # Continue conversation with optimized message history
                messages = conversation.get_messages_for_llm()
                response = await self.bedrock.converse(messages, self.bedrock_tools)
                
            elif stop_reason == 'end_turn':
                # Extract final response
                message_content = response['output']['message']['content']
                for item in message_content:
                    if 'text' in item:
                        final_text.append(item['text'])
                        # Add assistant response to conversation
                        conversation.add_message(Message.assistant(item['text']).__dict__, important=True)
                break
                
            elif stop_reason == 'max_tokens':
                final_text.append("[Response truncated due to max tokens]")
                break
            elif stop_reason == 'content_filtered':
                final_text.append("[Content was filtered]")
                break
            else:
                final_text.append(f"[Conversation ended: {stop_reason}]")
                break

            turn_count += 1

        if turn_count >= MAX_TURNS:
            final_text.append("\n[Max turns reached, ending conversation.]")

        return "\n".join(final_text)

    async def cleanup(self):
        """Clean up the chat session."""
        await self.tool_handler.cleanup_all()

# ------------------------
# Session Manager (New)
# ------------------------
class SessionManager:
    """Manages multiple chat sessions with cleanup."""
    
    def __init__(self, session_timeout: int = 3600):  # 1 hour timeout
        self.sessions: Dict[str, ConversationManager] = {}
        self.session_timeout = session_timeout
        self._cleanup_task: Optional[asyncio.Task] = None

    async def start_cleanup_task(self):
        """Start periodic cleanup of expired sessions."""
        self._cleanup_task = asyncio.create_task(self._periodic_cleanup())

    async def _periodic_cleanup(self):
        """Periodically clean up expired sessions."""
        while True:
            try:
                await asyncio.sleep(300)  # Check every 5 minutes
                current_time = time.time()
                expired_sessions = []
                
                for session_id, conversation in self.sessions.items():
                    # Check if session has been inactive
                    if hasattr(conversation, 'last_activity'):
                        if current_time - conversation.last_activity > self.session_timeout:
                            expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    logger.info(f"Cleaning up expired session: {session_id}")
                    del self.sessions[session_id]
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in session cleanup: {e}")

    def stop_cleanup_task(self):
        """Stop the cleanup task."""
        if self._cleanup_task:
            self._cleanup_task.cancel()

# ------------------------
# Enhanced Chat Session Class
# ------------------------
class EnhancedChatSession:
    def __init__(self, server_pool: ServerPool, bedrock: BedrockClient, config: Configuration):
        self.server_pool = server_pool
        self.bedrock = bedrock
        self.config = config
        self.tool_handler = ToolHandler(server_pool, config.max_concurrent_tools)
        self.tools = []
        self.bedrock_tools = []
        self.session_manager = SessionManager()

    async def initialize(self):
        """Initialize the enhanced chat session."""
        # Load server configuration
        config_data = Configuration.load_config("servers_config.json")
        
        # Initialize servers through tool handler
        await self.tool_handler.initialize_all_servers(config_data["mcpServers"])
        
        # Start health checks and session cleanup
        await self.server_pool.start_health_checks()
        await self.session_manager.start_cleanup_task()
        
        # Get all tools from all servers
        all_tools = await self.tool_handler.list_all_tools()
        self.tools = [ToolWrapper(t.name, t.description, t.input_schema) for t in all_tools]
        self.bedrock_tools = [tool.to_bedrock_format(use_compressed=self.config.use_compressed_schemas) for tool in self.tools]
        
        logger.info(f"Enhanced chat session initialized with {len(all_tools)} tools")

    async def process_message(self, message: str, session_id: str) -> str:
        """Process a single message and return the response."""
        conversation = ConversationManager(self.config.max_conversation_length)
        conversation.add_message(Message.user(message).__dict__)
        conversation.last_activity = time.time()
        
        # Store conversation in session manager
        self.session_manager.sessions[session_id] = conversation
        
        # Get optimized message history
        messages = conversation.get_messages_for_llm()
        
        # Process with Bedrock
        response = await self.bedrock.converse(messages, self.bedrock_tools)
        final_response = await self.process_response(response, conversation)
        
        conversation.last_activity = time.time()
        return final_response

    async def process_message_stream(self, message: str, session_id: str):
        """Process a message with streaming response."""
        conversation = await self._get_or_create_conversation(session_id)
        conversation.add_message(Message.user(message).__dict__)
        conversation.last_activity = time.time()
        
        # For streaming, we'd need to use Bedrock's streaming API
        # This is a simplified implementation
        response = await self.process_message(message, session_id)
        
        # Simulate streaming by yielding words
        words = response.split()
        for i, word in enumerate(words):
            chunk = word + (" " if i < len(words) - 1 else "")
            yield f"data: {json.dumps({'chunk': chunk, 'done': False})}\n\n"
            await asyncio.sleep(0.02)  # Faster streaming
        
        yield f"data: {json.dumps({'chunk': '', 'done': True})}\n\n"

    async def _get_or_create_conversation(self, session_id: str) -> ConversationManager:
        """Get or create conversation with proper session management."""
        if session_id in self.session_manager.sessions:
            conversation = self.session_manager.sessions[session_id]
        else:
            conversation = ConversationManager(self.config.max_conversation_length)
            self.session_manager.sessions[session_id] = conversation
        
        conversation.last_activity = time.time()
        return conversation

    async def _handle_tool_call(self, tool_info: Dict, conversation: ConversationManager) -> List[str]:
        """Handle a tool call with enhanced error handling and loop detection."""
        tool_name = tool_info['name']
        tool_args = tool_info['input']
        tool_use_id = tool_info['toolUseId']

        # Check for tool execution loops
        if not conversation.add_tool_execution(tool_name, tool_args):
            logger.warning(f"Tool execution loop detected for {tool_name}, skipping")
            return [f"[Tool execution loop detected for {tool_name}, skipping to prevent infinite loops]"]

        try:
            logger.info(f"Executing tool: {tool_name} with args: {tool_args}")
            result = await self.tool_handler.execute_tool(tool_name, tool_args)
            
            # Add tool request message
            tool_request_msg = Message.tool_request(tool_use_id, tool_name, tool_args).__dict__
            conversation.add_message(tool_request_msg)
            
            # Add tool result message
            tool_result_msg = Message.tool_result(tool_use_id, result)
            conversation.add_message(tool_result_msg, important=True)

            return [f"[Executed tool {tool_name}]"]
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            error_result = {
                "role": "user",
                "content": [
                    {
                        "toolResult": {
                            "toolUseId": tool_use_id,
                            "content": [{"text": f"Error executing tool: {str(e)}"}],
                            "isError": "True"
                        }
                    }
                ]
            }
            conversation.add_message(error_result)
            return [f"[Error executing tool {tool_name}: {str(e)}]"]

    async def _handle_concurrent_tool_calls(self, tool_calls: List[Dict], conversation: ConversationManager) -> List[str]:
        """Handle multiple tool calls concurrently."""
        # Prepare tool call data for concurrent execution
        tool_call_data = []
        for tool_call in tool_calls:
            tool_call_data.append({
                'name': tool_call['name'],
                'arguments': tool_call['input'],
                'toolUseId': tool_call['toolUseId']
            })
        
        # Execute tools concurrently
        results = await self.tool_handler.execute_tools_concurrently(tool_call_data)
        execution_results = []
        
        for tool_call, result in zip(tool_calls, results):
            tool_use_id = tool_call['toolUseId']
            tool_name = tool_call['name']
            
            # Add tool request message
            tool_request_msg = Message.tool_request(tool_use_id, tool_name, tool_call['input']).__dict__
            conversation.add_message(tool_request_msg)
            
            if isinstance(result, Exception):
                # Handle error
                error_result = {
                    "role": "user",
                    "content": [
                        {
                            "toolResult": {
                                "toolUseId": tool_use_id,
                                "content": [{"text": f"Error executing tool: {str(result)}"}],
                                "isError": "True"
                            }
                        }
                    ]
                }
                conversation.add_message(error_result)
                execution_results.append(f"[Error executing tool {tool_name}: {str(result)}]")
            else:
                # Handle success
                tool_result_msg = Message.tool_result(tool_use_id, result)
                conversation.add_message(tool_result_msg, important=True)
                execution_results.append(f"[Executed tool {tool_name}]")
        
        return execution_results
    
    async def process_response(self, response: dict, conversation: ConversationManager) -> str:
        """Process the response from Bedrock with enhanced tool handling."""
        final_text = []
        MAX_TURNS = 15
        turn_count = 0

        while turn_count < MAX_TURNS:
            stop_reason = response.get('stopReason', 'unknown')
            
            if stop_reason == 'tool_use':
                # Handle tool use
                message_content = response['output']['message']['content']
                tool_calls = []
                
                for item in message_content:
                    if 'text' in item and item['text'].strip():
                        thinking_text = f"[Thinking: {item['text']}]"
                        final_text.append(thinking_text)
                        conversation.add_message(Message.assistant(item['text']).__dict__)
                    elif 'toolUse' in item:
                        tool_calls.append(item['toolUse'])
                
                # Execute tools (concurrently if multiple)
                if len(tool_calls) > 1:
                    logger.info(f"Executing {len(tool_calls)} tools concurrently")
                    results = await self._handle_concurrent_tool_calls(tool_calls, conversation)
                elif len(tool_calls) == 1:
                    results = await self._handle_tool_call(tool_calls[0], conversation)
                else:
                    results = []
                
                final_text.extend(results)
                
                # Continue conversation with optimized message history
                messages = conversation.get_messages_for_llm()
                response = await self.bedrock.converse(messages, self.bedrock_tools)
                
            elif stop_reason == 'end_turn':
                # Extract final response
                message_content = response['output']['message']['content']
                for item in message_content:
                    if 'text' in item:
                        final_text.append(item['text'])
                        # Add assistant response to conversation
                        conversation.add_message(Message.assistant(item['text']).__dict__, important=True)
                break
                
            elif stop_reason == 'max_tokens':
                final_text.append("[Response truncated due to max tokens]")
                break
            elif stop_reason == 'content_filtered':
                final_text.append("[Content was filtered]")
                break
            else:
                final_text.append(f"[Conversation ended: {stop_reason}]")
                break

            turn_count += 1

        if turn_count >= MAX_TURNS:
            final_text.append("\n[Max turns reached, ending conversation.]")

        return "\n".join(final_text)

    async def cleanup(self):
        """Clean up the enhanced chat session."""
        self.session_manager.stop_cleanup_task()
        await self.tool_handler.cleanup_all()

# ------------------------
# Enhanced API Endpoints
# ------------------------
@app.on_event("startup")
async def startup_event():
    """Initialize the chat session on startup with enhanced error handling."""
    global chat_session, is_initialized
    
    async with initialization_lock:
        if is_initialized:
            return
            
        try:
            config = Configuration()
            
            # Validate environment variables (optional for demo)
            if not config.model_id:
                logger.warning("BEDROCK_MODEL_ID environment variable not set - running in demo mode")
            
            # Validate server configuration exists
            if not os.path.exists("servers_config.json"):
                raise ValueError("servers_config.json file not found")
            
            # Initialize server pool
            server_pool = ServerPool()
            
            # Initialize Bedrock client (optional for demo)
            bedrock = None
            if config.model_id:
                bedrock = BedrockClient(model_id=config.model_id, region=config.region)
            
            # Initialize enhanced chat session
            chat_session = EnhancedChatSession(server_pool, bedrock, config)
            await chat_session.initialize()
            
            is_initialized = True
            logger.info("Enhanced FastAPI application initialized successfully")
            
        except Exception as e:
            logger.error(f"Error during startup: {e}")
            is_initialized = False
            raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up resources on shutdown."""
    global chat_session, is_initialized
    
    if chat_session:
        await chat_session.cleanup()
    is_initialized = False
    logger.info("Application shutdown completed")

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Enhanced health check endpoint."""
    global chat_session, is_initialized
    
    available_tools = 0
    active_servers = 0
    
    if chat_session and is_initialized:
        available_tools = len(chat_session.tools)
        servers = await chat_session.server_pool.get_all_servers()
        active_servers = len([s for s in servers if s._initialized])
    
    return HealthResponse(
        status="healthy" if is_initialized else "initializing",
        initialized=is_initialized,
        available_tools=available_tools,
        active_servers=active_servers
    )

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(request: ChatRequest):
    """Enhanced chat endpoint with streaming support."""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        session_id = request.session_id or str(uuid.uuid4())
        
        if request.stream:
            # Return streaming response
            return StreamingResponse(
                chat_session.process_message_stream(request.message, session_id),
                media_type="text/plain"
            )
        else:
            # Return regular response
            response = await chat_session.process_message(request.message, session_id)
            
            return ChatResponse(
                response=response,
                session_id=session_id,
                status="success"
            )
        
    except Exception as e:
        logger.error(f"Error processing chat request: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/chat/{session_id}/stream")
async def chat_stream_endpoint(session_id: str, message: str):
    """Dedicated streaming endpoint."""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    return StreamingResponse(
        chat_session.process_message_stream(message, session_id),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )

@app.get("/tools")
async def list_tools():
    """Enhanced tools endpoint with server information."""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    tools_info = []
    server_info = {}
    
    # Get server metrics
    servers = await chat_session.server_pool.get_all_servers()
    for server in servers:
        metrics = chat_session.server_pool._metrics.get(server.name, ServerMetrics())
        server_info[server.name] = {
            "initialized": server._initialized,
            "success_count": metrics.success_count,
            "failure_count": metrics.failure_count,
            "avg_response_time": metrics.avg_response_time,
            "circuit_breaker_state": server.circuit_breaker.state.value
        }
    
    for tool in chat_session.tools:
        tools_info.append({
            "name": tool.name,
            "description": tool.description,
            "input_schema": tool.input_schema
        })
    
    return {
        "tools": tools_info, 
        "total_count": len(tools_info),
        "servers": server_info,
        "active_sessions": len(chat_session.session_manager.sessions)
    }

@app.get("/metrics")
async def get_metrics():
    """Get performance metrics."""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    servers = await chat_session.server_pool.get_all_servers()
    metrics = {}
    
    for server in servers:
        server_metrics = chat_session.server_pool._metrics.get(server.name, ServerMetrics())
        metrics[server.name] = {
            "success_count": server_metrics.success_count,
            "failure_count": server_metrics.failure_count,
            "avg_response_time": server_metrics.avg_response_time,
            "success_rate": (
                server_metrics.success_count / 
                max(1, server_metrics.success_count + server_metrics.failure_count)
            ),
            "circuit_breaker_state": server.circuit_breaker.state.value,
            "last_used": server_metrics.last_used
        }
    
    return {
        "server_metrics": metrics,
        "active_sessions": len(chat_session.session_manager.sessions),
        "total_tools": len( chat_session.tools),
        "token_usage": chat_session.bedrock.token_usage_stats
    }

@app.delete("/sessions/{session_id}")
async def clear_session(session_id: str):
    """Clear a specific session."""
    global chat_session
    
    if chat_session and session_id in chat_session.session_manager.sessions:
        del chat_session.session_manager.sessions[session_id]
        return {"message": f"Session {session_id} cleared"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

@app.post("/servers/{server_name}/restart")
async def restart_server(server_name: str):
    """Restart a specific server."""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    try:
        servers = await chat_session.server_pool.get_all_servers()
        target_server = None
        
        for server in servers:
            if server.name == server_name:
                target_server = server
                break
        
        if not target_server:
            raise HTTPException(status_code=404, detail=f"Server {server_name} not found")
        
        # Cleanup and reinitialize
        await target_server.cleanup()
        await target_server.initialize()
        
        # Rebuild tool mapping
        await chat_session.tool_handler._build_tool_mapping()
        
        return {"message": f"Server {server_name} restarted successfully"}
        
    except Exception as e:
        logger.error(f"Error restarting server {server_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# ------------------------
# Main Entry Point
# ------------------------
if __name__ == "__main__":
    uvicorn.run(
        "aws_agent:app",
        host="0.0.0.0",
        port=8001,
        reload=False,
        log_level="info",
        workers=1  # Important: Keep workers=1 for shared state
    )